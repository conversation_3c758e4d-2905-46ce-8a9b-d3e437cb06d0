# Project Planning Document: My Digital Brain (MVP)

**Project Title:** My Digital Brain (or "Gems Vault")

**Version:** 1.0 (MVP)
**Date:** June 3, 2025

---

#### **1. Project Vision & Core Principles**

**Vision:** To create a personal, local-first "digital brain" application that enables instantaneous capture of diverse digital information and highly intelligent, AI-powered retrieval, eliminating the need for manual organization.

**Core Principles:**
* **Effortless "Drop":** Capture information with minimal mental friction, instantly.
* **AI-Powered "Rewind":** Retrieve information by meaning and context, not just keywords.
* **Local-First Performance & Privacy:** Prioritize speed and data control on the user's device.
* **Free / Opt-in Charge:** Core features are free; multi-device sync is an opt-in, low-cost (for user) feature.
* **Unified Chat Interface:** All interactions (input and output) occur within a single chat window.

---

#### **2. MVP Features (Detailed Specification)**

**2.1. User Interface (UI)**

* **Primary Interface:** A single, minimalist chat window.
* **Input Field:** A prominent text input field at the bottom, similar to a messaging app.
* **Display Area:** Above the input field, the chat history will show both user inputs (dumps and queries) and AI responses (retrieved items, confirmations).

**2.2. Information Input ("Effortless Drop")**

* **Mechanism:** User types or pastes content into the chat input field and presses `Enter`.
* **Content Types:** Accepts raw text (clipboard), typed thoughts, and pasted URLs.
* **Differentiation from Queries:** User will use a simple, explicit command prefix for dumping:
    * `+ [content]` or `!d [content]` (e.g., `+ This new idea for marketing campaign.`)
    * The app interprets any input starting with `+` or `!d` as content to be dumped.
* **Immediate Save:** Upon hitting `Enter`, the content is instantly processed by AI and saved to the local database. No confirmation dialogs or extra steps.
* **Confirmation:** A subtle, brief confirmation message in the chat (e.g., "Gem saved!") will appear.

**2.3. Information Retrieval ("AI-Powered Rewind")**

* **Mechanism:** User types natural language queries into the same chat input field and presses `Enter`.
* **Differentiation from Dumps:** User will use a simple, explicit command prefix for retrieving:
    * `? [query]` or `!r [query]` (e.g., `? What was that code snippet for authentication?`)
    * The app interprets any input starting with `?` or `!r` as a retrieval query.
* **AI-Driven Search:**
    * The query is sent to the local Gemma 3N model to generate a semantic embedding.
    * This embedding is used to perform a semantic similarity search against all stored "gem" embeddings in the local vector database.
    * Results are ranked by relevance.
* **Display of Results:** Retrieved "gems" are displayed as a list within the chat interface, showing the raw `text_content`, `tags`, `type`, and `timestamp`. The AI will present these clearly (e.g., "Here are some gems related to your query:").

**2.4. On-Device AI Processing (Gemma 3N)**

* **Model:** Gemma 3N (or equivalent compact, performant local LLM).
* **Tasks on "Dump":**
    * **Semantic Embedding:** Generates a numerical vector representation for the dumped content.
    * **Auto-Tagging:** Generates multiple, detailed, and contextually relevant tags (e.g., `#project_X`, `#AI_ethics`, `#productivity`, `#frontend_dev`) for each gem. No restrictions on tag count or specificity.
    * **Content Type Identification:** Dynamically assigns a `type` to the gem based on its content (e.g., `idea`, `reference`, `code_snippet`, `url`, `personal_thought`, `task`).
    * **URL Extraction:** Automatically extracts URLs if present in the dumped content.
* **Tasks on "Retrieve":**
    * **Query Embedding:** Converts the user's natural language query into a semantic embedding.
    * **Semantic Search:** Facilitates meaning-based search against stored embeddings.

**2.5. Data Storage (Local Vector Database)**

* **Technology:** An embedded/local Vector Database (e.g., ChromaDB, LanceDB, or a suitable JavaScript/Python embedded vector store library that can run client-side in Electron/browser contexts).
* **Schema (per "Gem" entry):**
    * `id` (unique identifier)
    * `text_content` (the raw dumped text)
    * `embedding` (vector generated by Gemma 3N)
    * `tags` (array of auto-generated strings)
    * `type` (string, e.g., "url", "code", "idea")
    * `url` (string, nullable)
    * `timestamp` (datetime of dump)
    * *Optional for MVP:* `source_app` (string, if feasible to detect)

**2.6. Multi-Device Synchronization (Opt-in / Cloud Storage Sync)**

* **Mechanism:** Opt-in feature for users to link their app to a designated folder in their existing cloud storage service (e.g., Google Drive, Dropbox, OneDrive).
* **Sync Logic:** The app will manage syncing the local database file (the Vector DB's persistent data) to this cloud folder. Changes made on one device will be pushed to the cloud, and fetched by other linked devices.
* **Conflict Resolution:**
    * **Cloud Provider Version History:** Relies on the cloud service's native file versioning as a safety net.
    * **App-Level Alert & User Prompt:**
        * If the app detects a change in the cloud file that conflicts with local unsaved changes, it will alert the user in the chat interface.
        * The prompt will offer clear choices: "Keep my local changes (overwrite cloud)," or "Discard my local changes and load latest from cloud."

#### **3. Target Platforms**

* **Desktop App:** Cross-platform (Windows, macOS, Linux) via Electron.
* **Web App:** Accessible via modern web browsers (likely a Progressive Web App (PWA) for installability).
    * *Note for AI Tool:* The web app will require adjustments for local storage (e.g., IndexedDB for data persistence, WebAssembly for Gemma 3N if available, or a server-side component for AI processing if on-device web inference is too heavy for MVP). **Prioritize the desktop app first for local Gemma 3N, then adapt for web.**

#### **4. Technical Stack (Proposed)**

* **UI Framework:** React (with Electron for desktop, standard for web).
* **Local AI Model:** Gemma 3N (or suitable on-device LLM library/API wrapper).
* **Local Vector Database:** Choose a JavaScript/Python embedded library that can persist to disk (e.g., a lightweight client-side ChromaDB/LanceDB setup if suitable, or a similar in-memory DB with disk persistence).
* **Cloud Sync Integration:** Respective cloud service APIs (e.g., Google Drive API for file management).
* **Development Environment:** VS Code with AI coding assistants (Augmentcode, Cline.bot).

#### **5. MVP User Stories (Core Interactions)**

* As a user, I want to quickly paste text into the app and save it as a new "gem" without interruption, so I don't lose my focus.
* As a user, I want the app to automatically tag and identify the type of my saved "gems" using AI, so I don't have to organize them manually.
* As a user, I want to ask natural language questions in the chat to retrieve my "gems" by meaning, so I can easily find what I'm looking for.
* As a user, I want my "gems" to be stored locally on my device for privacy and speed.
* As a user, I want the option to sync my "gems" across multiple devices via my personal cloud storage, so my "Digital Brain" is always up-to-date.
* As a user, if a sync conflict occurs, I want the app to alert me and give me a clear choice on how to resolve it, so my data is protected.

#### **6. Non-Goals for MVP (Phase 1 Scope Exclusion)**

* No automatic summarization of content.
* No direct image handling (screenshots will be represented by text descriptions/keywords).
* No advanced visual organization (e.g., knowledge graphs, mind maps).
* No complex user management beyond basic local app setup.
* No external API integrations for enriching content (e.g., pulling article metadata beyond URL).
* No custom monetization within the app itself (reliance on user's cloud costs if applicable).